
-- Create KPI tables schema
CREATE TABLE IF NOT EXISTS kpi_1tox_rollback (
    id SERIAL PRIMARY KEY,
    lan VARCHAR(20),
    current_bucket INTEGER,
    mnt_rb_tag INTEGER,
    rsl_rb_cnt_tag INTEGER,
    prev_mnt_rb_tag INTEGER,
    insert_date TIMESTAMP,
    start_of_month TIMESTAMP,
    end_of_month TIMESTAMP,
    active_flag INTEGER,
    batch_id VARCHAR(50),
    source_insert_date TIMESTAMP,
    mnt_daily_rb_tag INTEGER,
    insert_date_db TIMESTAMP,
    pkcol BIGINT
);

CREATE TABLE IF NOT EXISTS kpi_mandates_table (
    id SERIAL PRIMARY KEY,
    finreference VARCHAR(20),
    agreementno VARCHAR(20),
    mandateid VARCHAR(20),
    finisactive INTEGER,
    reqmaturity TIMESTAMP,
    status VARCHAR(20),
    instnumber INTEGER,
    loan_status VARCHAR(5),
    productflag VARCHAR(5),
    bankbranchid VARCHAR(20),
    branchcode VARCHAR(20),
    branchdesc VARCHAR(100),
    bankcode VARCHAR(10),
    bankname VARCHAR(100),
    ifsccode VARCHAR(20),
    mnt_mandate_tag INTEGER,
    mnt_mandate_reject_tag INTEGER,
    mnt_femi_mandate_tag INTEGER,
    mnt_femi_mandate_reject_tag INTEGER,
    insert_date TIMESTAMP,
    start_of_month TIMESTAMP,
    end_of_month TIMESTAMP,
    active_flag INTEGER,
    batch_id VARCHAR(50),
    snapshot_ts TIMESTAMP,
    source_insert_date TIMESTAMP,
    first_due_date TIMESTAMP,
    insert_date_db TIMESTAMP,
    pkcol BIGINT
);

CREATE TABLE IF NOT EXISTS kpi_pns (
    id SERIAL PRIMARY KEY,
    lan VARCHAR(20),
    first_due_date DATE,
    current_bucket INTEGER,
    mob2_check DATE,
    mob3_check DATE,
    mob_2 INTEGER,
    mob_3 INTEGER,
    mnt_pns_tag INTEGER,
    rsl_pns_cnt_tag INTEGER,
    prev_mnt_pns_tag INTEGER,
    insert_date DATE,
    start_of_month DATE,
    end_of_month TIMESTAMP,
    active_flag INTEGER,
    batch_id VARCHAR(50),
    source_insert_date DATE,
    insert_date_db DATE,
    pkcol BIGINT
);

CREATE TABLE IF NOT EXISTS kpi_rc (
    id SERIAL PRIMARY KEY,
    branchname VARCHAR(50),
    branch_state VARCHAR(50),
    supplierid INTEGER,
    supplierdesc VARCHAR(200),
    agreementid INTEGER,
    agreementno VARCHAR(20),
    customername VARCHAR(100),
    product_code VARCHAR(10),
    current_bucket INTEGER,
    disbursaldate DATE,
    expiry_date DATE,
    period INTEGER,
    emi_amount INTEGER,
    asset VARCHAR(100),
    chassis_num VARCHAR(50),
    engine_num VARCHAR(50),
    invoiceno VARCHAR(50),
    invoicedate DATE,
    provisional_rc_flag VARCHAR(10),
    repayment_mode VARCHAR(20),
    bom_bucet INTEGER,
    scheme_name TEXT,
    sch_group VARCHAR(100),
    co_app_name VARCHAR(100),
    rc_grt_thn_60 INTEGER,
    rc_grt_thn_90 INTEGER,
    uid_no BIGINT,
    provisional_rcno VARCHAR(50),
    registrationnumber VARCHAR(50),
    npa_stageid VARCHAR(20),
    provisnalrcreason TEXT,
    insert_date DATE,
    start_of_month DATE,
    end_of_month TIMESTAMP,
    active_flag INTEGER,
    rc_pend_overall_cnt INTEGER,
    batch_id VARCHAR(50),
    snapshot_ts TIMESTAMP,
    source_insert_date DATE,
    insert_date_db DATE,
    pkcol BIGINT
);

CREATE TABLE IF NOT EXISTS kpi_femi (
    id SERIAL PRIMARY KEY,
    lan VARCHAR(20),
    mnt_femi_tag INTEGER,
    current_bucket INTEGER,
    instrument VARCHAR(10),
    feetypeid VARCHAR(20),
    mnt_femi_overdues_tag INTEGER,
    mnt_femi_rsld_tag INTEGER,
    insert_date DATE,
    start_of_month DATE,
    end_of_month TIMESTAMP,
    active_flag INTEGER,
    batch_id VARCHAR(50),
    snapshot_ts TIMESTAMP,
    source_insert_date DATE,
    insert_date_db DATE,
    pkcol BIGINT
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_kpi_1tox_rollback_lan ON kpi_1tox_rollback(lan);
CREATE INDEX IF NOT EXISTS idx_kpi_1tox_rollback_pkcol ON kpi_1tox_rollback(pkcol);
CREATE INDEX IF NOT EXISTS idx_kpi_mandates_finreference ON kpi_mandates_table(finreference);
CREATE INDEX IF NOT EXISTS idx_kpi_mandates_agreementno ON kpi_mandates_table(agreementno);
CREATE INDEX IF NOT EXISTS idx_kpi_pns_lan ON kpi_pns(lan);
CREATE INDEX IF NOT EXISTS idx_kpi_rc_agreementno ON kpi_rc(agreementno);
CREATE INDEX IF NOT EXISTS idx_kpi_femi_lan ON kpi_femi(lan);
