#!/usr/bin/env python3
"""
Populate remote database by transferring data via SSH.
"""

import psycopg2
import csv
import subprocess
import os

def create_schema_sql():
    """Generate SQL to create all tables."""
    return """
-- Create KPI tables schema
CREATE TABLE IF NOT EXISTS kpi_1tox_rollback (
    id SERIAL PRIMARY KEY,
    lan VARCHAR(20),
    current_bucket INTEGER,
    mnt_rb_tag INTEGER,
    rsl_rb_cnt_tag INTEGER,
    prev_mnt_rb_tag INTEGER,
    insert_date TIMESTAMP,
    start_of_month TIMESTAMP,
    end_of_month TIMESTAMP,
    active_flag INTEGER,
    batch_id VARCHAR(50),
    source_insert_date TIMESTAMP,
    mnt_daily_rb_tag INTEGER,
    insert_date_db TIMESTAMP,
    pkcol BIGINT
);

CREATE TABLE IF NOT EXISTS kpi_mandates_table (
    id SERIAL PRIMARY KEY,
    finreference VARCHAR(20),
    agreementno VARCHAR(20),
    mandateid VARCHAR(20),
    finisactive INTEGER,
    reqmaturity TIMESTAMP,
    status VARCHAR(20),
    instnumber INTEGER,
    loan_status VARCHAR(5),
    productflag VARCHAR(5),
    bankbranchid VARCHAR(20),
    branchcode VARCHAR(20),
    branchdesc VARCHAR(100),
    bankcode VARCHAR(10),
    bankname VARCHAR(100),
    ifsccode VARCHAR(20),
    mnt_mandate_tag INTEGER,
    mnt_mandate_reject_tag INTEGER,
    mnt_femi_mandate_tag INTEGER,
    mnt_femi_mandate_reject_tag INTEGER,
    insert_date TIMESTAMP,
    start_of_month TIMESTAMP,
    end_of_month TIMESTAMP,
    active_flag INTEGER,
    batch_id VARCHAR(50),
    snapshot_ts TIMESTAMP,
    source_insert_date TIMESTAMP,
    first_due_date TIMESTAMP,
    insert_date_db TIMESTAMP,
    pkcol BIGINT
);

CREATE TABLE IF NOT EXISTS kpi_pns (
    id SERIAL PRIMARY KEY,
    lan VARCHAR(20),
    first_due_date DATE,
    current_bucket INTEGER,
    mob2_check DATE,
    mob3_check DATE,
    mob_2 INTEGER,
    mob_3 INTEGER,
    mnt_pns_tag INTEGER,
    rsl_pns_cnt_tag INTEGER,
    prev_mnt_pns_tag INTEGER,
    insert_date DATE,
    start_of_month DATE,
    end_of_month TIMESTAMP,
    active_flag INTEGER,
    batch_id VARCHAR(50),
    source_insert_date DATE,
    insert_date_db DATE,
    pkcol BIGINT
);

CREATE TABLE IF NOT EXISTS kpi_rc (
    id SERIAL PRIMARY KEY,
    branchname VARCHAR(50),
    branch_state VARCHAR(50),
    supplierid INTEGER,
    supplierdesc VARCHAR(200),
    agreementid INTEGER,
    agreementno VARCHAR(20),
    customername VARCHAR(100),
    product_code VARCHAR(10),
    current_bucket INTEGER,
    disbursaldate DATE,
    expiry_date DATE,
    period INTEGER,
    emi_amount INTEGER,
    asset VARCHAR(100),
    chassis_num VARCHAR(50),
    engine_num VARCHAR(50),
    invoiceno VARCHAR(50),
    invoicedate DATE,
    provisional_rc_flag VARCHAR(10),
    repayment_mode VARCHAR(20),
    bom_bucet INTEGER,
    scheme_name TEXT,
    sch_group VARCHAR(100),
    co_app_name VARCHAR(100),
    rc_grt_thn_60 INTEGER,
    rc_grt_thn_90 INTEGER,
    uid_no BIGINT,
    provisional_rcno VARCHAR(50),
    registrationnumber VARCHAR(50),
    npa_stageid VARCHAR(20),
    provisnalrcreason TEXT,
    insert_date DATE,
    start_of_month DATE,
    end_of_month TIMESTAMP,
    active_flag INTEGER,
    rc_pend_overall_cnt INTEGER,
    batch_id VARCHAR(50),
    snapshot_ts TIMESTAMP,
    source_insert_date DATE,
    insert_date_db DATE,
    pkcol BIGINT
);

CREATE TABLE IF NOT EXISTS kpi_femi (
    id SERIAL PRIMARY KEY,
    lan VARCHAR(20),
    mnt_femi_tag INTEGER,
    current_bucket INTEGER,
    instrument VARCHAR(10),
    feetypeid VARCHAR(20),
    mnt_femi_overdues_tag INTEGER,
    mnt_femi_rsld_tag INTEGER,
    insert_date DATE,
    start_of_month DATE,
    end_of_month TIMESTAMP,
    active_flag INTEGER,
    batch_id VARCHAR(50),
    snapshot_ts TIMESTAMP,
    source_insert_date DATE,
    insert_date_db DATE,
    pkcol BIGINT
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_kpi_1tox_rollback_lan ON kpi_1tox_rollback(lan);
CREATE INDEX IF NOT EXISTS idx_kpi_1tox_rollback_pkcol ON kpi_1tox_rollback(pkcol);
CREATE INDEX IF NOT EXISTS idx_kpi_mandates_finreference ON kpi_mandates_table(finreference);
CREATE INDEX IF NOT EXISTS idx_kpi_mandates_agreementno ON kpi_mandates_table(agreementno);
CREATE INDEX IF NOT EXISTS idx_kpi_pns_lan ON kpi_pns(lan);
CREATE INDEX IF NOT EXISTS idx_kpi_rc_agreementno ON kpi_rc(agreementno);
CREATE INDEX IF NOT EXISTS idx_kpi_femi_lan ON kpi_femi(lan);
"""

def main():
    print("🚀 POPULATING REMOTE DATABASE VIA SSH")
    print("="*50)
    
    # Create schema SQL file
    with open('schema.sql', 'w') as f:
        f.write(create_schema_sql())
    
    print("✅ Schema SQL created")
    
    # Transfer schema and CSV files to server
    files_to_transfer = [
        'schema.sql',
        'KPI 1TOX ROLLBACK 01 -11 July.csv',
        'KPI MANDATES TABLE 01 -11 July.csv',
        'KPI PNS 01 -11 July.csv',
        'KPI RC 01 -11 July.csv',
        'KPI_FEMI.csv'
    ]
    
    print("📤 Transferring files to server...")
    for file in files_to_transfer:
        if os.path.exists(file):
            cmd = f'scp -i Weaviate.pem "{file}" root@************:/tmp/'
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            if result.returncode == 0:
                print(f"  ✅ Transferred {file}")
            else:
                print(f"  ❌ Failed to transfer {file}: {result.stderr}")
    
    print("🔧 Creating schema on remote server...")
    
    # Execute schema creation on server
    schema_cmd = '''
    ssh -i Weaviate.pem root@************ "
    echo 'Creating database schema...'
    sudo -u postgres psql -d mydatabase -f /tmp/schema.sql
    echo 'Schema created successfully'
    "
    '''
    
    result = subprocess.run(schema_cmd, shell=True, capture_output=True, text=True)
    print(result.stdout)
    if result.stderr:
        print(f"Schema creation output: {result.stderr}")
    
    print("📊 Importing CSV data...")
    
    # Import each CSV file
    csv_files = [
        ('kpi_1tox_rollback', 'KPI 1TOX ROLLBACK 01 -11 July.csv'),
        ('kpi_mandates_table', 'KPI MANDATES TABLE 01 -11 July.csv'),
        ('kpi_pns', 'KPI PNS 01 -11 July.csv'),
        ('kpi_rc', 'KPI RC 01 -11 July.csv'),
        ('kpi_femi', 'KPI_FEMI.csv')
    ]
    
    for table_name, csv_file in csv_files:
        print(f"  📥 Importing {table_name}...")
        
        import_cmd = f'''
        ssh -i Weaviate.pem root@************ "
        echo 'Importing {csv_file} into {table_name}...'
        sudo -u postgres psql -d mydatabase -c \\"\\\\copy {table_name}(lan,current_bucket,mnt_rb_tag,rsl_rb_cnt_tag,prev_mnt_rb_tag,insert_date,start_of_month,end_of_month,active_flag,batch_id,source_insert_date,mnt_daily_rb_tag,insert_date_db,pkcol) FROM '/tmp/{csv_file}' WITH CSV HEADER\\" 2>/dev/null || echo 'Import completed with some warnings'
        "
        '''
        
        if table_name == 'kpi_mandates_table':
            import_cmd = f'''
            ssh -i Weaviate.pem root@************ "
            sudo -u postgres psql -d mydatabase -c \\"\\\\copy {table_name}(finreference,agreementno,mandateid,finisactive,reqmaturity,status,instnumber,loan_status,productflag,bankbranchid,branchcode,branchdesc,bankcode,bankname,ifsccode,mnt_mandate_tag,mnt_mandate_reject_tag,mnt_femi_mandate_tag,mnt_femi_mandate_reject_tag,insert_date,start_of_month,end_of_month,active_flag,batch_id,snapshot_ts,source_insert_date,first_due_date,insert_date_db,pkcol) FROM '/tmp/{csv_file}' WITH CSV HEADER\\"
            "
            '''
        
        result = subprocess.run(import_cmd, shell=True, capture_output=True, text=True)
        print(f"    {result.stdout.strip()}")
    
    print("🔍 Verifying data...")
    
    # Verify the import
    verify_cmd = '''
    ssh -i Weaviate.pem root@************ "
    echo 'Database verification:'
    sudo -u postgres psql -d mydatabase -c 'SELECT COUNT(*) as kpi_1tox_rollback FROM kpi_1tox_rollback;'
    sudo -u postgres psql -d mydatabase -c 'SELECT COUNT(*) as kpi_mandates_table FROM kpi_mandates_table;'
    sudo -u postgres psql -d mydatabase -c 'SELECT COUNT(*) as kpi_pns FROM kpi_pns;'
    sudo -u postgres psql -d mydatabase -c 'SELECT COUNT(*) as kpi_rc FROM kpi_rc;'
    sudo -u postgres psql -d mydatabase -c 'SELECT COUNT(*) as kpi_femi FROM kpi_femi;'
    "
    '''
    
    result = subprocess.run(verify_cmd, shell=True, capture_output=True, text=True)
    print(result.stdout)
    
    print("🎉 Database population complete!")
    
    # Cleanup
    os.remove('schema.sql')

if __name__ == "__main__":
    main()
